/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: IBuryingPointRealTimeSubtitle.kt
 * Description: IBuryingPointRealTimeSubtitle.kt
 * Version: 1.0
 * Date: 2025/6/16
 * Author: W9017232
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9017232                         2025/6/16      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.modulerouter.recorder

interface IBuryingPointRealTimeSubtitle {
    fun addState(status: Boolean)
    fun addReturnSuccess()
    fun addReturnFail(reason: String)
    fun setFirstWordTime(time: Long)
    fun setBuryingPointValid(valid: Boolean)
    fun isSwitchEverTurnedOn(): Boolean
    fun reportBuryingPoint()
}